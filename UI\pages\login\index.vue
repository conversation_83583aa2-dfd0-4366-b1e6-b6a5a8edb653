<template>
  <view class="login-container" :class="{ 'page-loaded': pageLoaded }">
    <view class="login-content">
      <!-- Header section -->
      <view class="header-section" :class="{ 'animate-in': pageLoaded }">
        <text class="app-title">视频答题系统</text>
        <view class="title-decoration"></view>
      </view>

      <!-- Login form -->
      <view class="form-container" :class="{ 'animate-in': pageLoaded }">
        <!-- Username input -->
        <view class="input-group" :class="{ 'animate-in': pageLoaded }">
          <u-input v-model="loginForm.username" placeholder="请输入用户名" border="surround" clearable
            :error="!!errors.username" @blur="validateUsername" @input="clearError('username')" @focus="onInputFocus"
            @blur="onInputBlur" class="login-input" />
          <text v-if="errors.username" class="error-message animate-shake">{{ errors.username }}</text>
        </view>

        <!-- Password input -->
        <view class="input-group" :class="{ 'animate-in': pageLoaded }">
          <u-input v-model="loginForm.password" placeholder="请输入密码" border="surround" clearable
            :password="!showPassword" :error="!!errors.password" @blur="validatePassword"
            @input="clearError('password')" @focus="onInputFocus" @blur="onInputBlur" class="login-input">
            <template #suffix>
              <u-icon :name="showPassword ? 'eye' : 'eye-off'" size="20" color="#186BFF" @click="togglePassword"
                class="password-toggle" />
            </template>
          </u-input>
          <text v-if="errors.password" class="error-message animate-shake">{{ errors.password }}</text>
        </view>

        <!-- Login button -->
        <u-button type="primary" :text="isLoading ? '登录中...' : '登录'" :loading="isLoading"
          :disabled="!canSubmit || isLoading" @click="handleLogin" class="login-btn"
          :class="{ 'animate-in': pageLoaded, 'btn-loading': isLoading }" />

        <!-- Test accounts -->
        <view class="test-accounts">
          <text class="test-title">测试账号</text>
          <view class="test-grid">
            <u-button type="info" size="small" :disabled="isLoading" @click="quickLogin('super_admin_001', '123456')"
              class="test-btn test-btn-first">
              <view class="test-btn-content">
                <text class="test-username">super_admin_001</text>
                <text class="test-role">超管</text>
              </view>
            </u-button>

            <u-button type="info" size="small" :disabled="isLoading" @click="quickLogin('admin', '123456')"
              class="test-btn test-btn-middle">
              <view class="test-btn-content">
                <text class="test-username">admin</text>
                <text class="test-role">管理</text>
              </view>
            </u-button>

            <u-button type="info" size="small" :disabled="isLoading" @click="quickLogin('emp1', '123456')"
              class="test-btn test-btn-last">
              <view class="test-btn-content">
                <text class="test-username">emp1</text>
                <text class="test-role">员工1</text>
              </view>
            </u-button>
          </view>
        </view>
      </view>

      <!-- Footer -->
      <view class="footer">
        <text class="footer-text">© 2024 视频答题系统</text>
        <text class="footer-version">Version 1.0.0</text>
      </view>
    </view>

    <!-- Toast -->
    <u-toast ref="uToast" />
  </view>
</template>

<script>
import adminAuthService from '../../utils/adminAuthService.js'

export default {
  data () {
    return {
      loginForm: {
        username: '',
        password: ''
      },
      showPassword: false,
      isLoading: false,
      pageLoaded: false,
      errors: {
        username: '',
        password: ''
      }
    }
  },
  computed: {
    canSubmit () {
      return this.loginForm.username.trim() &&
        this.loginForm.password.trim() &&
        !this.isLoading
    }
  },
  onLoad () {
    this.checkExistingLogin()
    // 延迟触发页面加载动画
    setTimeout(() => {
      this.pageLoaded = true
    }, 100)
  },
  methods: {

    resetForm () {
      this.loginForm = {
        username: '',
        password: ''
      }
      this.errors = {
        username: '',
        password: ''
      }
      this.showPassword = false
      this.isLoading = false
    },

    togglePassword () {
      this.showPassword = !this.showPassword
    },

    // 输入框聚焦动效
    onInputFocus (e) {
      // 可以在这里添加聚焦时的动效逻辑
    },

    onInputBlur (e) {
      // 可以在这里添加失焦时的动效逻辑
    },

    validateUsername () {
      const username = this.loginForm.username.trim()
      if (!username) {
        this.errors.username = '请输入用户名'
        return false
      }
      if (username.length < 3) {
        this.errors.username = '用户名至少3个字符'
        return false
      }
      if (!/^[a-zA-Z0-9_]+$/.test(username)) {
        this.errors.username = '用户名只能包含字母、数字和下划线'
        return false
      }
      this.errors.username = ''
      return true
    },

    validatePassword () {
      const password = this.loginForm.password.trim()
      if (!password) {
        this.errors.password = '请输入密码'
        return false
      }
      if (password.length < 6) {
        this.errors.password = '密码至少6个字符'
        return false
      }
      this.errors.password = ''
      return true
    },

    clearError (field) {
      if (this.errors[field]) {
        this.errors[field] = ''
      }
    },

    validateForm () {
      const usernameValid = this.validateUsername()
      const passwordValid = this.validatePassword()
      return usernameValid && passwordValid
    },

    async handleLogin () {
      // Validate form before submission
      if (!this.validateForm()) {
        this.showToastMessage('请检查输入信息', 'error')
        return
      }

      if (!this.canSubmit || this.isLoading) return

      this.isLoading = true

      try {
        const { username, password } = this.loginForm
        const result = await adminAuthService.authenticate(username, password)

        if (result.success) {
          this.showToastMessage(result.message, 'success')
          await this.delay(1000)
          adminAuthService.redirectToMain()
        } else {
          this.showToastMessage(result.message, 'error')
          // Clear password on failed login for security
          this.loginForm.password = ''
        }
      } catch (error) {
        console.error('Login error:', error)
        this.showToastMessage('登录失败，请重试', 'error')
        this.loginForm.password = ''
      } finally {
        this.isLoading = false
      }
    },

    checkExistingLogin () {
      // Check if admin is already logged in and session is valid
      if (adminAuthService.isLoggedIn() && adminAuthService.isSessionValid()) {
        adminAuthService.redirectToMain()
      } else if (adminAuthService.isLoggedIn()) {
        // Session expired, logout
        adminAuthService.logout()
      }
    },

    showToastMessage (message, type = 'success') {
      // 使用 uview-plus 的 toast 组件
      this.$refs.uToast.show({
        message: message,
        type: type,
        duration: 3000
      })
    },

    delay (ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },

    // 测试账号快速登录方法 - 上线时删除
    quickLogin (username, password) {
      if (this.isLoading) return

      // 填充表单
      this.loginForm.username = username
      this.loginForm.password = password

      // 清除错误信息
      this.errors.username = ''
      this.errors.password = ''

      // 执行登录
      this.handleLogin()
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f2f5 0%, #fafbfc 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transition: opacity 0.6s ease;
}

.login-container.page-loaded {
  opacity: 1;
}

/* 背景装饰 */
.login-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(24, 107, 255, 0.03) 1px, transparent 1px);
  background-size: 50rpx 50rpx;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.login-content {
  width: 100%;
  max-width: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.header-section {
  text-align: center;
  margin-bottom: 60rpx;
  transform: translateY(30rpx);
  opacity: 0;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-section.animate-in {
  transform: translateY(0);
  opacity: 1;
}

.app-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #186BFF;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(24, 107, 255, 0.1);
}

.title-decoration {
  width: 60rpx;
  height: 4rpx;
  background: #186BFF;
  margin: 0 auto;
  border-radius: 2rpx;
  transform: scaleX(0);
  transition: transform 0.6s ease 0.3s;
}

.header-section.animate-in .title-decoration {
  transform: scaleX(1);
}


.form-container {
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 48rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(24, 107, 255, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transform: translateY(30rpx);
  opacity: 0;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
}

.form-container.animate-in {
  transform: translateY(0);
  opacity: 1;
}

.input-group {
  margin-bottom: 32rpx;
  transform: translateY(20rpx);
  opacity: 0;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-group.animate-in {
  transform: translateY(0);
  opacity: 1;
}

.input-group:nth-child(1).animate-in {
  transition-delay: 0.4s;
}

.input-group:nth-child(2).animate-in {
  transition-delay: 0.5s;
}

.error-message {
  font-size: 24rpx;
  color: #F5222D;
  margin-top: 12rpx;
  margin-left: 8rpx;
  animation: slideIn 0.3s ease;
}

.error-message.animate-shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5rpx);
  }

  75% {
    transform: translateX(5rpx);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-8rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 输入框样式 */
.login-input {
  background-color: rgba(255, 255, 255, 0.8) !important;
  border-radius: 12rpx !important;
  height: 96rpx !important;
  transition: all 0.3s ease !important;
}

.login-input:focus-within {
  background-color: rgba(255, 255, 255, 1) !important;
  box-shadow: 0 0 0 2rpx rgba(24, 107, 255, 0.2) !important;
  transform: translateY(-2rpx);
}

.password-toggle {
  transition: all 0.3s ease;
}

.password-toggle:active {
  transform: scale(0.9);
}

/* 登录按钮样式 */
.login-btn {
  width: 100% !important;
  height: 96rpx !important;
  background: #186BFF !important;
  border-radius: 12rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  margin-top: 48rpx !important;
  margin-bottom: 32rpx !important;
  box-shadow: 0 6rpx 20rpx rgba(24, 107, 255, 0.3) !important;
  transform: translateY(20rpx);
  opacity: 0;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.6s, transform 0.2s ease !important;
}

.login-btn.animate-in {
  transform: translateY(0);
  opacity: 1;
}

.login-btn:active {
  transform: translateY(2rpx) !important;
  box-shadow: 0 4rpx 12rpx rgba(24, 107, 255, 0.4) !important;
}

.login-btn.btn-loading {
  background: #91d5ff !important;
  cursor: not-allowed;
}

/* 测试账号区域 */
.test-accounts {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #eee;
}

.test-title {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-bottom: 20rpx;
}

.test-grid {
  display: flex;
  gap: 20rpx;
  justify-content: space-between;
}

/* 测试账号按钮共用样式 */
.test-btn {
  flex: 1 !important;
  background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%) !important;
  border: 1rpx solid rgba(25, 118, 210, 0.2) !important;
  border-radius: 12rpx !important;
  padding: 1.5rem 1rem !important;
  font-size: 24rpx !important;
  box-shadow: 0 2rpx 8rpx rgba(25, 118, 210, 0.1) !important;
}

.test-btn-first {
  margin-right: 10rpx !important;
}

.test-btn-middle {
  margin-right: 10rpx !important;
}

.test-btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.test-username {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.test-role {
  font-size: 20rpx;
  color: #666;
}

.footer {
  margin-top: 60rpx;
  text-align: center;
  opacity: 0.7;
}

.footer-text {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.footer-version {
  font-size: 22rpx;
  color: #999;
}
</style>
