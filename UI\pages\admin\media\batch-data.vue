<template>
    <view class="data-page" :class="{ 'page-loaded': pageLoaded }">
        <!-- 页面头部 -->
        <view class="page-header" :class="{ 'animate-in': pageLoaded }">
            <view class="header-content">
                <view class="back-section">
                    <view class="back-btn" @click="goBack">
                        <text class="back-icon">‹</text>
                        <text class="back-text">返回</text>
                    </view>
                </view>
                <view class="title-section">
                    <text class="page-title">批次数据</text>
                    <text class="page-subtitle" v-if="batchData.title">{{ batchData.title }}</text>
                </view>
            </view>
        </view>

        <!-- 数据展示组件 -->
        <BatchDataDisplay :batchId="batchId" :batchData="batchData" :pageLoaded="pageLoaded" />
    </view>
</template>

<script>
import { getBatchDetail } from "@/api/batch.js";
import BatchDataDisplay from "@/components/BatchDataDisplay.vue";

export default {
    name: 'BatchDataPage',
    components: {
        BatchDataDisplay
    },
    data () {
        return {
            batchId: '',
            pageLoaded: false,
            batchData: {
                id: '',
                title: '',
                totalViews: 0,
                totalReward: 0,
                totalStudents: 0
            }
        }
    },
    async onLoad (options) {
        // 触发页面加载动画
        setTimeout(() => {
            this.pageLoaded = true;
        }, 100);

        if (options && options.id) {
            this.batchId = options.id;
            await this.loadBatchData();
        } else {
            uni.showToast({
                title: '批次ID缺失',
                icon: 'none'
            });
            setTimeout(() => {
                uni.navigateBack();
            }, 1500);
        }
    },
    methods: {
        async loadBatchData () {
            try {
                uni.showLoading({
                    title: "加载中...",
                });

                const response = await getBatchDetail(this.batchId);

                if (response.success && response.data) {
                    const batch = response.data;
                    this.batchData = {
                        id: batch.id,
                        title: batch.name || batch.title,
                        totalViews: batch.currentParticipants || 0,
                        totalReward: batch.rewardAmount || 0,
                        totalStudents: batch.totalStudents || batch.currentParticipants || 0,
                        redPacketAmount: batch.redPacketAmount || 0,
                        // 传递完整的统计数据
                        statistics: batch.statistics || null
                    };

                    console.log('批次数据加载完成:', this.batchData);
                } else {
                    throw new Error(response.msg || '获取批次数据失败');
                }

                uni.hideLoading();
            } catch (error) {
                uni.hideLoading();
                uni.showToast({
                    title: "加载失败",
                    icon: "none",
                });
            }
        },
        goBack () {
            uni.navigateBack();
        }
    }
}
</script>

<style lang="scss">
@import '@/styles/index.scss';

.data-page {
    width: 100%;
    min-height: 100vh;
    background: linear-gradient(180deg, #f8faff 0%, #ffffff 100%);
    opacity: 0;
    transition: opacity 0.6s ease;
}

.data-page.page-loaded {
    opacity: 1;
}

/* 页面头部 */
.page-header {
    background: #186BFF;
    padding: 40rpx 32rpx 32rpx;
    color: white;
    box-shadow: 0 4rpx 12rpx rgba(24, 107, 255, 0.15);
    transform: translateY(-20rpx);
    opacity: 0;
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-header.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 24rpx;
}

.back-section {
    flex-shrink: 0;
}

.back-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 12rpx 20rpx;
    background: rgba(255, 255, 255, 0.15);
    border: 1rpx solid rgba(255, 255, 255, 0.25);
    border-radius: 12rpx;
    color: white;
    font-size: 28rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:active {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(1rpx) scale(0.98);
    }

    &:hover {
        background: rgba(255, 255, 255, 0.2);
    }
}

.back-icon {
    font-size: 32rpx;
    font-weight: bold;
    transition: transform 0.3s ease;
}

.back-btn:active .back-icon {
    transform: translateX(-2rpx);
}

.back-text {
    font-size: 28rpx;
    font-weight: 500;
}

.title-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6rpx;
    min-width: 0;
}

.page-title {
    font-size: 36rpx;
    font-weight: 600;
    color: white;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.page-subtitle {
    font-size: 26rpx;
    color: rgba(255, 255, 255, 0.85);
    opacity: 0.9;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
