<template>
    <view class="batch-data-container">
        <!-- 我的数据标题 -->
        <view class="data-title-section" :class="{ 'animate-in': pageLoaded }">
            <view class="data-subtitle">数据概况</view>
        </view>

        <!-- 详细统计卡片 -->
        <view class="stats-grid" v-if="!loading" :class="{ 'animate-in': pageLoaded }">
            <view class="stat-card-item">
                <view class="stat-content">
                    <text class="stat-value">{{ getTotalStudents() }}</text>
                    <text class="stat-label">学员数 </text>
                </view>
            </view>
            <view class="stat-card-item">
                <view class="stat-content">
                    <text class="stat-value">{{ getWatchingStudents() }}</text>
                    <text class="stat-label">观看学员数 </text>
                </view>
            </view>
            <view class="stat-card-item">
                <view class="stat-content">
                    <text class="stat-value">{{ getWatchingRate() }}%</text>
                    <text class="stat-label">观看率 </text>
                </view>
            </view>
            <view class="stat-card-item">
                <view class="stat-content">
                    <text class="stat-value">{{ getCompletedCount() }}</text>
                    <text class="stat-label">完播学员数 </text>
                </view>
            </view>
            <view class="stat-card-item">
                <view class="stat-content">
                    <text class="stat-value">{{ getCompletionRate() }}%</text>
                    <text class="stat-label">完播率 </text>
                </view>
            </view>
            <view class="stat-card-item">
                <view class="stat-content">
                    <text class="stat-value">{{ getAnswerStudents() }}</text>
                    <text class="stat-label">答题学员数 </text>
                </view>
            </view>
            <view class="stat-card-item">
                <view class="stat-content">
                    <text class="stat-value">{{ getAnswerRate() }}%</text>
                    <text class="stat-label">答题率 </text>
                </view>
            </view>
            <view class="stat-card-item">
                <view class="stat-content">
                    <text class="stat-value">{{ getRedPacketCount() }}</text>
                    <text class="stat-label">领取红包个数 </text>
                </view>
            </view>
            <view class="stat-card-item">
                <view class="stat-content">
                    <text class="stat-value">¥ {{ calculateTotalReward() }}</text>
                    <text class="stat-label">领取红包金额 </text>
                </view>
            </view>
        </view>

        <!-- 基础数据标题 -->
        <view class="basic-data-section">
            <text class="basic-data-title">基础数据</text>
        </view>

        <!-- 筛选器 -->
        <view class="filter-section" :class="{ 'animate-in': pageLoaded }">
            <view class="tabs-container">
                <view v-for="(tab, index) in tabsList" :key="index" class="tab-item"
                    :class="{ 'tab-active': currentTabIndex === index }" @click="onTabChange(index)">
                    {{ tab.name }}
                </view>
            </view>
        </view>

        <!-- 加载状态 -->
        <view class="loading-container" v-if="loading" :class="{ 'animate-in': pageLoaded }">
            <text class="loading-text">正在加载数据...</text>
        </view>

        <!-- 用户列表 -->
        <view class="user-list-container" v-if="!loading" :class="{ 'animate-in': pageLoaded }">
            <view v-for="(user, index) in filteredUsers" :key="index" @click="viewUserDetail(user)" class="user-card">
                <view class="user-item-content">
                    <view class="user-avatar-component">
                        <image :src="user.avatar" class="user-avatar" mode="aspectFill"></image>
                    </view>

                    <view class="user-info">
                        <view class="user-header">
                            <view class="user-name">{{ user.name }}</view>
                            <view class="status-badge"
                                :class="user.progress === 100 ? 'completed' : (user.progress > 0 ? 'incomplete' : 'unwatched')">
                                {{ user.progress === 100 ? '已完播' : (user.progress > 0 ? '未完播' : '未观看') }}
                            </view>
                        </view>

                        <view class="progress-section">
                            <view class="progress-info">
                                <text class="progress-label">观看进度</text>
                                <text class="progress-value"
                                    :class="user.progress === 100 ? 'completed' : (user.progress > 0 ? 'incomplete' : 'unwatched')">
                                    {{ user.progress }}%
                                </text>
                            </view>
                            <view class="progress-component">
                                <view class="progress-bar">
                                    <view class="progress-fill" :style="{
                                        width: user.progress + '%',
                                        backgroundColor: user.progress === 100 ? '#1890ff' : (user.progress > 0 ? '#fa8c16' : '#d9d9d9')
                                    }"></view>
                                </view>
                            </view>
                        </view>

                        <view class="user-details">
                            <view class="detail-item">
                                <text class="detail-label">观看时间</text>
                                <text class="detail-value">{{ formatDate(user.viewTime) }}</text>
                            </view>
                            <view class="detail-item">
                                <text class="detail-label">获得奖励</text>
                                <text class="detail-value reward">{{ user.reward.toFixed(2) }}元</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <view v-if="filteredUsers.length === 0" class="empty-container">
                <text class="empty-text">暂无观看用户</text>
            </view>
        </view>
    </view>
</template>

<script>
// 新的统一API
import { getBatchStatistics, getBatchRecords } from '@/api/user-batch-record.js'
// 保留旧API作为备用
import { getBatchViewRecords, getViewStatistics } from '@/api/view-record.js'
import { getAnswerStatistics } from '@/api/answer-record.js'
import { getBatchStatistics as getOldBatchStatistics } from '@/api/batch.js'
import mediaCommonMixin from "@/mixins/media-common.js"

export default {
    name: 'BatchDataDisplay',
    mixins: [mediaCommonMixin],
    props: {
        batchId: {
            type: String,
            required: true
        },
        batchData: {
            type: Object,
            default: () => ({})
        },
        pageLoaded: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            viewingUsers: [],
            currentTab: 'watched',
            currentTabIndex: 0,
            tabsList: [
                { name: '已观看' },
                { name: '未观看' }
            ],
            // 真实统计数据
            viewStatistics: {
                totalViews: 0,
                totalUsers: 0,
                completedUsers: 0,
                completionRate: 0,
                totalReward: 0
            },
            answerStatistics: {
                totalAnswers: 0,
                answerRate: 0,
                correctRate: 0
            },
            loading: false
        }
    },
    computed: {
        filteredUsers () {
            let users = [...this.viewingUsers];

            // 根据标签过滤
            if (this.currentTab === 'watched') {
                users = users.filter(user => user.progress > 0);
            } else if (this.currentTab === 'unwatched') {
                users = users.filter(user => user.progress === 0);
            }

            return users;
        }
    },
    async mounted () {
        if (this.batchId) {
            await this.loadRealData();
        }
    },
    watch: {
        batchId: {
            async handler (newVal, oldVal) {
                // 只有当 batchId 真正变化且有值时才重新加载
                if (newVal && newVal !== oldVal) {
                    await this.loadRealData();
                }
            }
        }
    },
    methods: {
        // 加载真实数据
        async loadRealData () {
            if (!this.batchId || this.loading) return;

            this.loading = true;

            try {
                // 优先使用新的统一API
                const [batchStats, batchRecords] = await Promise.all([
                    this.loadNewBatchStatistics(),
                    this.loadNewBatchRecords()
                ]);

                if (batchStats && batchRecords) {
                    console.log('使用新API加载数据成功:', { batchStats, batchRecords });
                    return;
                }

                // 如果新API失败，回退到旧API
                console.log('新API失败，回退到旧API');
                await this.loadViewRecords();

                // 如果批次数据中没有统计信息，则加载额外的统计数据
                if (!this.batchData.statistics) {
                    await Promise.all([
                        this.loadViewStatistics(),
                        this.loadAnswerStatistics()
                    ]);
                }
            } catch (error) {
                console.error('加载真实数据失败:', error);
                uni.showToast({
                    title: '数据加载失败',
                    icon: 'none'
                });
            } finally {
                this.loading = false;
            }
        },

        // 加载观看记录
        async loadViewRecords () {
            try {
                const response = await getBatchViewRecords(this.batchId, {
                    pageIndex: 1,
                    pageSize: 50 // 获取前50条记录
                });

                if (response.success && response.data) {
                    this.viewingUsers = response.data.items?.map(record => ({
                        id: record.userId,
                        name: record.userName || record.userNickname || `用户${record.userId}`,
                        avatar: record.userAvatar ? this.buildCompleteFileUrl(record.userAvatar) : '/static/images/avatar-placeholder.png',
                        viewTime: this.formatDate(record.startTime),
                        progress: Math.round((record.watchProgress || 0) * 100),
                        reward: record.rewardAmount || 0
                    })) || [];

                    return response.data;
                }
            } catch (error) {
                console.error('加载观看记录失败:', error);
                return null;
            }
        },

        // 加载观看统计
        async loadViewStatistics () {
            try {
                const response = await getViewStatistics({
                    batchId: this.batchId
                });

                if (response.success && response.data) {
                    this.viewStatistics = {
                        totalViews: response.data.totalViews || 0,
                        totalUsers: response.data.totalUsers || 0,
                        completedUsers: response.data.completedUsers || 0,
                        completionRate: response.data.completionRate || 0,
                        totalReward: response.data.totalReward || 0
                    };

                    return response.data;
                }
            } catch (error) {
                console.error('加载观看统计失败:', error);
                return null;
            }
        },

        // 加载答题统计
        async loadAnswerStatistics () {
            try {
                const response = await getAnswerStatistics(this.batchId);

                if (response.success && response.data) {
                    this.answerStatistics = {
                        totalAnswers: response.data.totalAnswers || 0,
                        answerRate: response.data.answerRate || 0,
                        correctRate: response.data.correctRate || 0
                    };

                    return response.data;
                }
            } catch (error) {
                console.error('加载答题统计失败:', error);
                return null;
            }
        },

        // 使用新API加载批次统计数据
        async loadNewBatchStatistics () {
            try {
                const response = await getBatchStatistics(this.batchId);

                if (response.success && response.data) {
                    // 更新统计数据
                    this.viewStatistics = {
                        totalViews: response.data.viewerCount,
                        totalUsers: response.data.totalParticipants,
                        completedUsers: response.data.completedViewerCount,
                        completionRate: response.data.completeRate,
                        totalReward: response.data.totalRewardAmount
                    };

                    this.answerStatistics = {
                        totalAnswers: response.data.answerCount,
                        answerRate: response.data.answerRate,
                        correctRate: response.data.averageCorrectRate
                    };

                    return response.data;
                }
            } catch (error) {
                console.error('加载新批次统计失败:', error);
                return null;
            }
        },

        // 使用新API加载批次记录
        async loadNewBatchRecords () {
            try {
                const response = await getBatchRecords(this.batchId);

                if (response.success && response.data) {
                    this.viewingUsers = response.data.map(record => ({
                        id: record.userId,
                        name: record.userNickname || `用户${record.userId}`,
                        avatar: record.userAvatar ? this.buildCompleteFileUrl(record.userAvatar) : '/static/images/avatar-placeholder.png',
                        viewTime: this.formatDate(record.startTime),
                        progress: Math.round(record.watchProgressPercent || 0),
                        reward: record.rewardAmount || 0,
                        isCompleted: record.isCompleted,
                        hasAnswered: record.hasAnswered,
                        correctRate: record.correctRate || 0,
                        rewardStatus: record.rewardStatus,
                        rewardStatusText: record.rewardStatusText
                    }));

                    return response.data;
                }
            } catch (error) {
                console.error('加载新批次记录失败:', error);
                return null;
            }
        },

        // 加载批次统计数据（备用方法）
        async loadBatchStatistics () {
            try {
                const response = await getOldBatchStatistics(this.batchId);

                if (response.success && response.data) {
                    // 不要直接修改props的batchData，避免触发循环
                    return response.data;
                }
            } catch (error) {
                console.error('加载批次统计失败:', error);
                return null;
            }
        },



        formatDate (date) {
            if (!date) return '未观看';
            if (typeof date === 'string') return date;

            return date.toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        onTabChange (index) {
            this.currentTabIndex = index;

            // 根据索引设置对应的 tab 值
            const tabMap = {
                0: 'watched',
                1: 'unwatched'
            };

            this.currentTab = tabMap[index];
        },

        getTotalStudents () {
            // 优先使用批次统计数据
            if (this.batchData.statistics) {
                return this.batchData.statistics.totalCount || this.batchData.statistics.activeCount || 0;
            }

            // 备用数据源
            return this.viewStatistics.totalUsers ||
                this.batchData.totalStudents ||
                this.batchData.totalViews ||
                this.viewingUsers.length || 0;
        },

        getWatchingStudents () {
            // 优先使用批次统计数据
            if (this.batchData.statistics) {
                return this.batchData.statistics.viewCount || 0;
            }

            // 备用数据源
            return this.viewStatistics.totalUsers ||
                this.viewingUsers.filter(user => user.progress > 0).length;
        },

        getWatchingRate () {
            const total = this.getTotalStudents();
            const watching = this.getWatchingStudents();
            if (total === 0) return 0;
            return Math.round((watching / total) * 100 * 100) / 100;
        },

        getCompletedCount () {
            // 优先使用批次统计数据
            if (this.batchData.statistics) {
                return this.batchData.statistics.completeViewCount || this.batchData.statistics.completedCount || 0;
            }

            // 备用数据源
            return this.viewStatistics.completedUsers ||
                this.viewingUsers.filter(user => user.progress === 100).length;
        },

        getCompletionRate () {
            // 优先使用批次统计数据
            if (this.batchData.statistics && this.batchData.statistics.completeRate !== undefined) {
                return Math.round(this.batchData.statistics.completeRate * 100 * 100) / 100;
            }

            // 备用计算
            if (this.viewStatistics.completionRate > 0) {
                return Math.round(this.viewStatistics.completionRate * 100) / 100;
            }

            const watching = this.getWatchingStudents();
            const completed = this.getCompletedCount();
            if (watching === 0) return 0;
            return Math.round((completed / watching) * 100 * 100) / 100;
        },

        getAnswerStudents () {
            // 优先使用批次统计数据
            if (this.batchData.statistics) {
                return this.batchData.statistics.totalAnswerCount || 0;
            }

            // 备用数据源
            if (this.answerStatistics.totalAnswers > 0) {
                return this.answerStatistics.totalAnswers;
            }

            // 最后备用计算
            const completed = this.getCompletedCount();
            const incomplete = this.viewingUsers.filter(user => user.progress > 0 && user.progress < 100).length;
            return completed + Math.floor(incomplete * 0.5);
        },

        getAnswerRate () {
            // 优先使用批次统计数据计算答题率
            if (this.batchData.statistics) {
                const totalAnswers = this.batchData.statistics.totalAnswerCount || 0;
                const watching = this.getWatchingStudents();
                if (watching > 0) {
                    return Math.round((totalAnswers / watching) * 100 * 100) / 100;
                }
            }

            // 备用数据源
            if (this.answerStatistics.answerRate > 0) {
                return Math.round(this.answerStatistics.answerRate * 100) / 100;
            }

            const watching = this.getWatchingStudents();
            const answered = this.getAnswerStudents();
            if (watching === 0) return 0;
            return Math.round((answered / watching) * 100 * 100) / 100;
        },

        getRedPacketCount () {
            // 优先使用批次统计数据
            if (this.batchData.statistics) {
                return this.batchData.statistics.rewardCount || 0;
            }

            // 备用：领取红包个数等于完播用户数
            return this.getCompletedCount();
        },

        viewUserDetail (user) {
            // 直接跳转到用户详情页面
            uni.navigateTo({
                url: `/pages/admin/users/info?userId=${user.id}`
            });
        },

        calculateTotalReward () {
            // 优先使用批次统计数据
            if (this.batchData.statistics && this.batchData.statistics.rewardAmount !== undefined) {
                return this.batchData.statistics.rewardAmount.toFixed(2);
            }

            // 备用：使用批次基本数据
            if (this.batchData.redPacketAmount !== undefined) {
                return this.batchData.redPacketAmount.toFixed(2);
            }

            // 备用：使用观看统计数据
            if (this.viewStatistics.totalReward > 0) {
                return this.viewStatistics.totalReward.toFixed(2);
            }

            // 最后备用：从用户记录中累加
            return this.viewingUsers.reduce((total, user) => total + (user.reward || 0), 0).toFixed(2);
        },

        formatDate (dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
        },


    }
}
</script>

<style lang="scss">
@import '../styles/index.scss';

.batch-data-container {
    width: 100%;
    background: #f8faff;
    min-height: 100vh;
}

/* 数据标题区域 */
.data-title-section {
    background: #186BFF;
    padding: 24rpx 32rpx;
    color: white;
    transform: translateY(-10rpx);
    opacity: 0;
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
}

.data-title-section.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.data-title {
    font-size: 32rpx;
    font-weight: 600;
    color: white;
    margin-bottom: 6rpx;
}

.data-subtitle {
    font-size: 26rpx;
    color: rgba(255, 255, 255, 0.9);
    border-left: 4rpx solid rgba(255, 255, 255, 0.8);
    padding-left: 12rpx;
}

/* 统计数据网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rpx;
    background: #e6f7ff;
    overflow: hidden;
    transform: translateY(20rpx);
    opacity: 0;
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
}

.stats-grid.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.stat-card-item {
    background: white;
    padding: 24rpx 12rpx;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: scale(0.95);
    opacity: 0;
    animation: cardFadeIn 0.6s ease forwards;

    &:hover {
        background: #f0f9ff;
        transform: translateY(-2rpx);
    }

    &:active {
        transform: translateY(1rpx) scale(0.98);
    }
}

.stats-grid.animate-in .stat-card-item {
    &:nth-child(1) {
        animation-delay: 0.1s;
    }

    &:nth-child(2) {
        animation-delay: 0.2s;
    }

    &:nth-child(3) {
        animation-delay: 0.3s;
    }

    &:nth-child(4) {
        animation-delay: 0.4s;
    }

    &:nth-child(5) {
        animation-delay: 0.5s;
    }

    &:nth-child(6) {
        animation-delay: 0.6s;
    }

    &:nth-child(7) {
        animation-delay: 0.7s;
    }

    &:nth-child(8) {
        animation-delay: 0.8s;
    }

    &:nth-child(9) {
        animation-delay: 0.9s;
    }
}

@keyframes cardFadeIn {
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.stat-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
}

.stat-value {
    font-size: 40rpx;
    font-weight: 700;
    color: #186BFF;
    line-height: 1.2;
}

.stat-label {
    font-size: 22rpx;
    color: #666;
    font-weight: 500;
    line-height: 1.3;
    text-align: center;
}

.help-icon {
    display: inline-block;
    width: 20rpx;
    height: 20rpx;
    background: #91d5ff;
    color: white;
    border-radius: 50%;
    font-size: 16rpx;
    line-height: 20rpx;
    text-align: center;
    margin-left: 6rpx;
}

/* 基础数据标题 */
.basic-data-section {
    padding: 24rpx 32rpx 16rpx;
}

.basic-data-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #186BFF;
    border-left: 4rpx solid #186BFF;
    padding-left: 12rpx;
}

/* 筛选器 */
.filter-section {
    padding: 0 32rpx 16rpx;
    transform: translateY(20rpx);
    opacity: 0;
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.6s;
}

.filter-section.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.tabs-container {
    display: flex;
    background: #ffffff;
    border-radius: 12rpx;
    padding: 4rpx;
    box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.1);
    margin-bottom: 16rpx;
}

.tab-item {
    flex: 1;
    text-align: center;
    padding: 16rpx 20rpx;
    font-size: 28rpx;
    color: #666666;
    border-radius: 8rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    font-weight: 500;

    &:active {
        transform: scale(0.98);
    }
}

.tab-active {
    background: #186BFF;
    color: #ffffff !important;
    font-weight: 600;
    box-shadow: 0 2rpx 8rpx rgba(24, 107, 255, 0.3);
    transform: translateY(-1rpx);
}

/* 用户列表容器 */
.user-list-container {
    padding: 0 32rpx 32rpx;
    transform: translateY(20rpx);
    opacity: 0;
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.8s;
}

.user-list-container.animate-in {
    transform: translateY(0);
    opacity: 1;
}

/* 用户头像 */
.user-avatar-component {
    margin-right: 20rpx;
    flex-shrink: 0;
}

.user-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 8rpx;
    background-color: #f0f9ff;
    border: 2rpx solid #91d5ff;
    box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.1);
}

/* 用户卡片 */
.user-card {
    display: block !important;
    background: #ffffff;
    border: 1rpx solid #e6f7ff;
    box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border-radius: 16rpx;
    margin-bottom: 16rpx;
    padding: 24rpx;
    transform: translateY(10rpx);
    opacity: 0;
    animation: userCardFadeIn 0.6s ease forwards;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4rpx;
        height: 100%;
        background: #186BFF;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    &:active {
        transform: translateY(1rpx) scale(0.98);
        box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.15);

        &::before {
            opacity: 1;
        }
    }

    &:hover {
        transform: translateY(-2rpx);
        box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.12);
    }
}

.user-list-container.animate-in .user-card {
    &:nth-child(1) {
        animation-delay: 0.1s;
    }

    &:nth-child(2) {
        animation-delay: 0.2s;
    }

    &:nth-child(3) {
        animation-delay: 0.3s;
    }

    &:nth-child(4) {
        animation-delay: 0.4s;
    }

    &:nth-child(5) {
        animation-delay: 0.5s;
    }
}

@keyframes userCardFadeIn {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.user-item-content {
    display: flex;
    align-items: flex-start;
}

.user-info {
    flex: 1;
}

.user-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;
}

.user-name {
    font-size: 30rpx;
    font-weight: 600;
    color: #1a1a1a;
}

.status-badge {
    padding: 6rpx 12rpx;
    border-radius: 12rpx;
    font-size: 20rpx;
    font-weight: 500;
    letter-spacing: 0.5rpx;

    &.completed {
        background: #e6f7ff;
        color: #186BFF;
        border: 1rpx solid #91d5ff;
    }

    &.incomplete {
        background: #fff7e6;
        color: #fa8c16;
        border: 1rpx solid #ffd591;
    }

    &.unwatched {
        background: #f5f5f5;
        color: #999999;
        border: 1rpx solid #d9d9d9;
    }
}

.progress-section {
    margin-bottom: 16rpx;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8rpx;
}

.progress-label {
    font-size: 24rpx;
    color: #666666;
    font-weight: 500;
}

.progress-value {
    font-size: 24rpx;
    font-weight: 600;

    &.completed {
        color: #186BFF;
    }

    &.incomplete {
        color: #fa8c16;
    }

    &.unwatched {
        color: #999999;
    }
}

.progress-component {
    margin-top: 8rpx;
}

.progress-bar {
    width: 100%;
    height: 8rpx;
    background-color: #f0f9ff;
    border-radius: 4rpx;
    overflow: hidden;
    border: 1rpx solid #e6f7ff;
}

.progress-fill {
    height: 100%;
    border-radius: 4rpx;
    transition: width 0.3s ease;
    background: linear-gradient(90deg, #186BFF, #40a9ff);
}

.user-details {
    display: flex;
    justify-content: space-between;
    margin-top: 6rpx;
}

.detail-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    background: #f0f9ff;
    padding: 12rpx 16rpx;
    border-radius: 8rpx;
    flex: 1;
    margin: 0 6rpx;
    border: 1rpx solid #e6f7ff;
    min-width: 0; // 允许flex项目收缩

    &:first-child {
        margin-left: 0;
        flex: 1.2; // 给观看时间更多空间
    }

    &:last-child {
        margin-right: 0;
        flex: 0.8; // 奖励金额占用较少空间
    }
}

.detail-label {
    font-size: 20rpx;
    color: #999999;
    margin-bottom: 4rpx;
    font-weight: 500;
}

.detail-value {
    font-size: 24rpx;
    color: #333;
    font-weight: 600;
    word-break: break-all; // 允许长文本换行
    line-height: 1.3;

    &.reward {
        color: #186BFF;
        font-weight: 700;
    }
}

/* 加载状态 */
.loading-container {
    text-align: center;
    padding: 120rpx 32rpx;
    background: white;
    border-radius: 16rpx;
    margin: 0 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.08);
    border: 1rpx solid #e6f7ff;
}

.loading-text {
    font-size: 30rpx;
    color: #186BFF;
    font-weight: 500;
}

/* 空状态 */
.empty-container {
    text-align: center;
    padding: 80rpx 32rpx;
    background: white;
    border-radius: 16rpx;
    margin: 0 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.08);
    border: 1rpx solid #e6f7ff;
}

.empty-text {
    font-size: 28rpx;
    color: #999999;
    font-weight: 500;
}
</style>
